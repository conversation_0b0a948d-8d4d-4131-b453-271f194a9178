import React, { useState } from 'react';
import { CVAdmin } from './CVAdmin';
import './AdminButton.css';

export const AdminButton: React.FC = () => {
  const [showAdmin, setShowAdmin] = useState(false);

  return (
    <>
      <button 
        className="admin-button"
        onClick={() => setShowAdmin(true)}
        title="Administrar CV"
      >
        <i className="bi bi-gear-fill"></i>
        Admin
      </button>
      
      {showAdmin && (
        <CVAdmin onClose={() => setShowAdmin(false)} />
      )}
    </>
  );
};
