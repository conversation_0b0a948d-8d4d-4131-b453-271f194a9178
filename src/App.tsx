import { useEffect } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import './style.css';

import Header from './components/Header';
import Hero from './components/Hero';
import About from './components/About';
import Skills from './components/Skills';
import Resume from './components/Resume';
import Portfolio from './components/Portfolio';
import Services from './components/Services';
import Footer from './components/Footer';
import { AdminButton } from './components/admin/AdminButton';

import { useCVDataReadOnly } from './hooks/useCVData';

function App() {
  const { cvData, loading, error } = useCVDataReadOnly();

  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: 'ease-in-out',
      once: true,
      mirror: false
    });
  }, []);

  if (loading) {
    return (
      <div className="loading-screen">
        <div className="loading-spinner"></div>
        <p>Cargando CV...</p>
      </div>
    );
  }

  if (error || !cvData) {
    return (
      <div className="error-screen">
        <h2>Error al cargar el CV</h2>
        <p>{error || 'No se pudieron cargar los datos'}</p>
        <button onClick={() => window.location.reload()}>
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <>
      {import.meta.env.DEV && <AdminButton />}ç

      <Header personalInfo={cvData.personalInfo} />

      <Hero personalInfo={cvData.personalInfo} />

      <main id="main">
        <About
          personalInfo={cvData.personalInfo}
          aboutText={cvData.about}
        />

        <Skills skills={cvData.skills} />

        <Resume
          education={cvData.education}
          complementaryEducation={cvData.complementaryEducation}
          experience={cvData.experience}
        />

        <Portfolio portfolio={cvData.portfolio} />

        <Services services={cvData.services} />
      </main>

      <Footer />
    </>
  );
}

export default App;
