import React, { useState, useEffect } from 'react';

const Footer: React.FC = () => {
  const [showBackToTop, setShowBackToTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      <footer id="footer">
        <div className="container">
          <div className="copyright">
            &copy; Copyright <strong><span>Msarknet</span></strong>
          </div>
        </div>
      </footer>

      <a
        href="#"
        className={`back-to-top d-flex align-items-center justify-content-center ${showBackToTop ? 'active' : ''}`}
        onClick={(e) => { e.preventDefault(); scrollToTop(); }}
      >
        <i className="bi bi-arrow-up-short"></i>
      </a>
    </>
  );
};

export default Footer;
