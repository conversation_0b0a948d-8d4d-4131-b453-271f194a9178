# CV I<PERSON> - React Portfolio

Este es un CV/Portfolio personal desarrollado con React, TypeScript y Vite. La aplicación presenta información profesional, habilidades, experiencia y proyectos de manera interactiva y responsive.

## 🚀 Características

- **React 19** con TypeScript para type safety
- **Vite** para desarrollo rápido y build optimizado
- **Responsive Design** con Bootstrap 5
- **Animaciones** con AOS (Animate On Scroll) y Framer Motion
- **Efectos de escritura** con Typed.js
- **Navegación suave** entre secciones
- **Filtros interactivos** para skills y portfolio
- **Mobile-first** design con navegación móvil

## 🛠️ Tecnologías Utilizadas

- React 19.1.1
- TypeScript 5.8.3
- Vite 7.1.6
- Bootstrap 5.3.0
- AOS (Animate On Scroll)
- Typed.js
- Framer Motion
- Bootstrap Icons
- Boxicons

## 📦 Instalación

1. Clona el repositorio:
```bash
git clone [url-del-repositorio]
cd curriculum_vitae
```

2. Instala las dependencias:
```bash
npm install
```

3. Inicia el servidor de desarrollo:
```bash
npm run dev
```

4. Abre tu navegador en `http://localhost:5174`

## 🏗️ Scripts Disponibles

- `npm run dev` - Inicia el servidor de desarrollo
- `npm run build` - Construye la aplicación para producción
- `npm run lint` - Ejecuta ESLint para verificar el código
- `npm run preview` - Previsualiza la build de producción

## 📁 Estructura del Proyecto

```
src/
├── components/          # Componentes React
│   ├── Header.tsx      # Navegación principal
│   ├── Hero.tsx        # Sección hero con efecto typing
│   ├── About.tsx       # Información personal
│   ├── Skills.tsx      # Habilidades con filtros
│   ├── Resume.tsx      # Experiencia y educación
│   ├── Portfolio.tsx   # Proyectos y trabajos
│   ├── Services.tsx    # Servicios ofrecidos
│   └── Footer.tsx      # Footer con botón back-to-top
├── data/               # Datos del CV
│   └── cvData.ts       # Información personal y profesional
├── hooks/              # Custom hooks
│   └── useActiveSection.ts # Hook para navegación activa
├── types/              # Definiciones TypeScript
│   └── cv.ts           # Interfaces del CV
├── App.tsx             # Componente principal
├── main.tsx            # Punto de entrada
└── style.css           # Estilos principales
```

## 🎨 Personalización

Para personalizar el CV con tu información:

1. **Datos personales**: Edita `src/data/cvData.ts`
2. **Imágenes**: Reemplaza las imágenes en `public/img/`
3. **Estilos**: Modifica `src/style.css` para cambiar colores y diseño
4. **Componentes**: Ajusta los componentes en `src/components/` según necesites

## 📱 Responsive Design

La aplicación está optimizada para:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## 🔧 Funcionalidades Implementadas

- ✅ Navegación suave entre secciones
- ✅ Indicador de sección activa en el menú
- ✅ Filtros interactivos para skills y portfolio
- ✅ Animaciones de entrada con AOS
- ✅ Efecto de escritura en el hero
- ✅ Botón back-to-top con scroll detection
- ✅ Navegación móvil responsive
- ✅ Barras de progreso animadas para skills

## 🚀 Deployment

Para hacer deploy de la aplicación:

1. Construye la aplicación:
```bash
npm run build
```

2. Los archivos estáticos se generarán en la carpeta `dist/`

3. Puedes deployar estos archivos en cualquier servicio de hosting estático como:
   - Vercel
   - Netlify
   - GitHub Pages
   - Firebase Hosting

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 🔄 Migración desde HTML

Este proyecto fue migrado desde una versión HTML estática ubicada en `_crear_como_react/`. 
La migración incluyó:

- Conversión de HTML a componentes React
- Implementación de TypeScript para type safety
- Modernización de JavaScript vanilla a React hooks
- Optimización de assets y estructura de archivos
- Implementación de routing y navegación SPA
