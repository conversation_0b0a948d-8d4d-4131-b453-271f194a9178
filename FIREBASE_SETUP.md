# 🔥 Firebase Setup para CV Dinámico

Este documento explica cómo configurar y usar el sistema de Firebase para gestionar dinámicamente los datos del CV.

## 📋 Características Implementadas

✅ **Almacenamiento en Firestore**: Todos los datos del CV se guardan en Firebase Firestore
✅ **Carga Dinámica**: Los datos se cargan automáticamente desde Firebase al iniciar la aplicación
✅ **Panel de Administración**: Interfaz completa para editar todos los datos del CV
✅ **Versionado**: Sistema de versiones para mantener un historial de cambios
✅ **Modo Offline**: Fallback a datos locales si Firebase no está disponible
✅ **Estados de Carga**: Pantallas de carga y error para mejor UX

## 🚀 Configuración Inicial

### 1. Verificar Configuración de Firebase

Tu proyecto ya tiene Firebase configurado en `src/firebase/config.ts`. Las credenciales están configuradas para el proyecto `cerebro-8d986`.

### 2. Configurar Reglas de Firestore

En la consola de Firebase, ve a **Firestore Database > Rules** y configura las siguientes reglas:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Permitir lectura pública de los datos del CV
    match /cv_data/{document} {
      allow read: if true;
      allow write: if request.auth != null; // Solo usuarios autenticados pueden escribir
    }
    
    // Permitir lectura pública de las versiones
    match /cv_versions/{document} {
      allow read: if true;
      allow write: if request.auth != null; // Solo usuarios autenticados pueden escribir
    }
  }
}
```

### 3. Inicializar Datos

Para subir los datos por defecto a Firebase por primera vez:

1. Abre la aplicación en el navegador
2. Abre las herramientas de desarrollador (F12)
3. Ve a la consola
4. Ejecuta el siguiente comando:

```javascript
await initializeFirebaseData();
```

Esto subirá todos los datos del archivo `cvData.ts` a Firebase.

## 🎛️ Uso del Panel de Administración

### Acceder al Panel

1. En la aplicación, verás un botón **"Admin"** en la esquina superior derecha
2. Haz clic para abrir el panel de administración

### Funcionalidades del Panel

#### ✏️ **Pestaña "Editar CV"**
- **Información Personal**: Edita nombre, título, teléfono, ciudad
- **Acerca de**: Modifica la descripción personal
- **Habilidades**: 
  - Agregar/eliminar habilidades
  - Cambiar porcentajes
  - Asignar categorías
- **Guardar**: Incluye descripción opcional de los cambios

#### 📚 **Pestaña "Versiones"**
- Ver historial completo de cambios
- Restaurar versiones anteriores
- Eliminar versiones no necesarias
- Ver fecha y descripción de cada cambio

## 🔧 Estructura de Datos en Firebase

### Colección: `cv_data`
```
cv_data/
  current/
    personalInfo: {...}
    about: "..."
    skills: [...]
    education: [...]
    complementaryEducation: [...]
    experience: [...]
    portfolio: [...]
    services: [...]
    lastUpdated: timestamp
```

### Colección: `cv_versions`
```
cv_versions/
  {versionId}/
    data: {CVData completo}
    version: "v1", "v2", etc.
    createdAt: timestamp
    isActive: boolean
    description: "Descripción del cambio"
```

## 🛠️ API de Servicios

### CVService

```typescript
// Obtener datos actuales
const cvData = await CVService.getCurrentCV();

// Guardar CV completo
await CVService.saveCV(cvData, "Descripción del cambio");

// Actualizar solo una sección
await CVService.updateSection('skills', newSkills);

// Gestión de versiones
const versions = await CVService.getVersions();
await CVService.restoreVersion(versionId);
await CVService.deleteVersion(versionId);
```

### Hook useCVData

```typescript
const {
  cvData,           // Datos actuales del CV
  versions,         // Lista de versiones
  loading,          // Estado de carga
  saving,           // Estado de guardado
  error,            // Errores
  saveCV,           // Función para guardar
  updateSection,    // Función para actualizar sección
  restoreVersion,   // Función para restaurar versión
  deleteVersion,    // Función para eliminar versión
  refreshData,      // Función para recargar datos
  clearError        // Función para limpiar errores
} = useCVData();
```

## 🔒 Seguridad y Autenticación

### Configuración Actual
- **Lectura**: Pública (cualquiera puede ver el CV)
- **Escritura**: Requiere autenticación

### Para Habilitar Autenticación (Opcional)

1. En Firebase Console, ve a **Authentication**
2. Habilita el método de autenticación deseado (Google, Email, etc.)
3. Modifica el componente `CVAdmin` para incluir autenticación:

```typescript
import { auth } from '../firebase';
import { signInWithPopup, GoogleAuthProvider } from 'firebase/auth';

// En el componente
const handleLogin = async () => {
  const provider = new GoogleAuthProvider();
  await signInWithPopup(auth, provider);
};
```

## 🚨 Solución de Problemas

### Error: "Permission denied"
- Verifica las reglas de Firestore
- Asegúrate de estar autenticado si las reglas lo requieren

### Error: "Firebase not initialized"
- Verifica la configuración en `src/firebase/config.ts`
- Asegúrate de que las credenciales sean correctas

### Datos no se cargan
- Verifica la conexión a internet
- Revisa la consola del navegador para errores
- Ejecuta `initializeFirebaseData()` si es la primera vez

### Panel de administración no aparece
- Verifica que el botón Admin esté visible
- Revisa errores en la consola del navegador

## 📱 Responsive y Móvil

El panel de administración está optimizado para dispositivos móviles:
- Diseño adaptativo
- Formularios táctiles
- Navegación simplificada en pantallas pequeñas

## 🔄 Flujo de Trabajo Recomendado

1. **Desarrollo Local**: Usa datos estáticos para desarrollo
2. **Primera Implementación**: Ejecuta `initializeFirebaseData()`
3. **Actualizaciones**: Usa el panel de administración
4. **Respaldos**: Las versiones automáticas sirven como respaldo
5. **Restauración**: Usa el historial de versiones si algo sale mal

## 📈 Próximas Mejoras

- [ ] Autenticación con Google/GitHub
- [ ] Subida de imágenes a Firebase Storage
- [ ] Editor WYSIWYG para descripciones
- [ ] Exportar/Importar datos en JSON
- [ ] Notificaciones push para cambios
- [ ] Analytics de visualizaciones del CV
