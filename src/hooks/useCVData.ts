import { useState, useEffect, useCallback } from 'react';
import { CVService } from '../services/cvService';
import type { CVVersion } from '../services/cvService';
import type { CVData } from '../types/cv';
import { cvData as defaultCVData } from '../data/cvData';

interface UseCVDataReturn {
  // Estado de los datos
  cvData: CVData | null;
  versions: CVVersion[];

  // Estados de carga
  loading: boolean;
  saving: boolean;
  error: string | null;

  // Acciones
  saveCV: (data: CVData, description?: string) => Promise<void>;
  updateSection: <K extends keyof CVData>(section: K, data: CVData[K]) => Promise<void>;
  restoreVersion: (versionId: string) => Promise<void>;
  deleteVersion: (versionId: string) => Promise<void>;
  refreshData: () => Promise<void>;
  clearError: () => void;
}

/**
 * Hook personalizado para manejar los datos del CV desde Firebase
 */
export const useCVData = (): UseCVDataReturn => {
  const [cvData, setCVData] = useState<CVData | null>(null);
  const [versions, setVersions] = useState<CVVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Carga los datos iniciales del CV
   */
  const loadCVData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Intentar cargar datos desde Firebase
      let data = await CVService.getCurrentCV();

      // Si no hay datos, inicializar con datos por defecto
      if (!data) {
        await CVService.initializeCV(defaultCVData);
        data = defaultCVData;
      }

      setCVData(data);
    } catch (err) {
      console.error('Error al cargar datos del CV:', err);
      setError('Error al cargar los datos del CV');
      // En caso de error, usar datos por defecto
      setCVData(defaultCVData);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Carga las versiones del CV
   */
  const loadVersions = useCallback(async () => {
    try {
      const versionsList = await CVService.getVersions();
      setVersions(versionsList);
    } catch (err) {
      console.error('Error al cargar versiones:', err);
      setError('Error al cargar las versiones del CV');
    }
  }, []);

  /**
   * Refresca todos los datos
   */
  const refreshData = useCallback(async () => {
    await Promise.all([loadCVData(), loadVersions()]);
  }, [loadCVData, loadVersions]);

  /**
   * Guarda el CV completo
   */
  const saveCV = useCallback(async (data: CVData, description?: string) => {
    try {
      setSaving(true);
      setError(null);

      await CVService.saveCV(data, description);
      setCVData(data);

      // Recargar versiones para mostrar la nueva
      await loadVersions();
    } catch (err) {
      console.error('Error al guardar CV:', err);
      setError('Error al guardar el CV');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [loadVersions]);

  /**
   * Actualiza una sección específica del CV
   */
  const updateSection = useCallback(async <K extends keyof CVData>(
    section: K,
    data: CVData[K]
  ) => {
    try {
      setSaving(true);
      setError(null);

      await CVService.updateSection(section, data);

      // Actualizar estado local
      setCVData(prev => prev ? { ...prev, [section]: data } : null);
    } catch (err) {
      console.error(`Error al actualizar sección ${section}:`, err);
      setError(`Error al actualizar la sección ${section}`);
      throw err;
    } finally {
      setSaving(false);
    }
  }, []);

  /**
   * Restaura una versión específica
   */
  const restoreVersion = useCallback(async (versionId: string) => {
    try {
      setSaving(true);
      setError(null);

      await CVService.restoreVersion(versionId);

      // Recargar datos después de restaurar
      await refreshData();
    } catch (err) {
      console.error('Error al restaurar versión:', err);
      setError('Error al restaurar la versión');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [refreshData]);

  /**
   * Elimina una versión
   */
  const deleteVersion = useCallback(async (versionId: string) => {
    try {
      setError(null);

      await CVService.deleteVersion(versionId);

      // Actualizar lista de versiones
      setVersions(prev => prev.filter(v => v.id !== versionId));
    } catch (err) {
      console.error('Error al eliminar versión:', err);
      setError('Error al eliminar la versión');
      throw err;
    }
  }, []);

  /**
   * Limpia el error actual
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Cargar datos iniciales
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  return {
    cvData,
    versions,
    loading,
    saving,
    error,
    saveCV,
    updateSection,
    restoreVersion,
    deleteVersion,
    refreshData,
    clearError
  };
};

/**
 * Hook simplificado para solo obtener los datos del CV (solo lectura)
 */
export const useCVDataReadOnly = () => {
  const [cvData, setCVData] = useState<CVData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        let data = await CVService.getCurrentCV();

        if (!data) {
          data = defaultCVData;
        }

        setCVData(data);
      } catch (err) {
        console.error('Error al cargar datos:', err);
        setError('Error al cargar los datos');
        setCVData(defaultCVData);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return { cvData, loading, error };
};
