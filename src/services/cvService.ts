import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  collection, 
  getDocs, 
  addDoc, 
  deleteDoc,
  query,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '../firebase';
import type { CVData } from '../types/cv';

// Constantes para las colecciones
const CV_COLLECTION = 'cv_data';
const CV_VERSIONS_COLLECTION = 'cv_versions';

// Interfaz para versiones del CV
export interface CVVersion {
  id?: string;
  data: CVData;
  version: string;
  createdAt: Timestamp;
  isActive: boolean;
  description?: string;
}

/**
 * Servicio para manejar los datos del CV en Firebase
 */
export class CVService {
  
  /**
   * Obtiene los datos actuales del CV
   */
  static async getCurrentCV(): Promise<CVData | null> {
    try {
      const docRef = doc(db, CV_COLLECTION, 'current');
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return docSnap.data() as CVData;
      } else {
        console.log('No se encontraron datos del CV');
        return null;
      }
    } catch (error) {
      console.error('Error al obtener los datos del CV:', error);
      throw error;
    }
  }

  /**
   * Guarda o actualiza los datos del CV
   */
  static async saveCV(cvData: CVData, description?: string): Promise<void> {
    try {
      // Guardar como versión actual
      const currentDocRef = doc(db, CV_COLLECTION, 'current');
      await setDoc(currentDocRef, {
        ...cvData,
        lastUpdated: serverTimestamp()
      });

      // Crear una nueva versión en el historial
      await this.createVersion(cvData, description);
      
      console.log('CV guardado exitosamente');
    } catch (error) {
      console.error('Error al guardar el CV:', error);
      throw error;
    }
  }

  /**
   * Crea una nueva versión del CV en el historial
   */
  static async createVersion(cvData: CVData, description?: string): Promise<string> {
    try {
      // Obtener el número de versión siguiente
      const versions = await this.getVersions();
      const nextVersion = `v${versions.length + 1}`;

      // Desactivar todas las versiones anteriores
      for (const version of versions) {
        if (version.id && version.isActive) {
          const versionRef = doc(db, CV_VERSIONS_COLLECTION, version.id);
          await updateDoc(versionRef, { isActive: false });
        }
      }

      // Crear nueva versión
      const versionData: Omit<CVVersion, 'id'> = {
        data: cvData,
        version: nextVersion,
        createdAt: serverTimestamp() as Timestamp,
        isActive: true,
        description: description || `Versión ${nextVersion}`
      };

      const docRef = await addDoc(collection(db, CV_VERSIONS_COLLECTION), versionData);
      console.log('Nueva versión creada:', nextVersion);
      return docRef.id;
    } catch (error) {
      console.error('Error al crear versión:', error);
      throw error;
    }
  }

  /**
   * Obtiene todas las versiones del CV
   */
  static async getVersions(): Promise<CVVersion[]> {
    try {
      const q = query(
        collection(db, CV_VERSIONS_COLLECTION), 
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CVVersion));
    } catch (error) {
      console.error('Error al obtener versiones:', error);
      throw error;
    }
  }

  /**
   * Restaura una versión específica del CV
   */
  static async restoreVersion(versionId: string): Promise<void> {
    try {
      const versionRef = doc(db, CV_VERSIONS_COLLECTION, versionId);
      const versionSnap = await getDoc(versionRef);
      
      if (!versionSnap.exists()) {
        throw new Error('Versión no encontrada');
      }

      const versionData = versionSnap.data() as CVVersion;
      await this.saveCV(versionData.data, `Restaurada desde ${versionData.version}`);
      
      console.log('Versión restaurada exitosamente');
    } catch (error) {
      console.error('Error al restaurar versión:', error);
      throw error;
    }
  }

  /**
   * Elimina una versión del historial
   */
  static async deleteVersion(versionId: string): Promise<void> {
    try {
      const versionRef = doc(db, CV_VERSIONS_COLLECTION, versionId);
      await deleteDoc(versionRef);
      console.log('Versión eliminada exitosamente');
    } catch (error) {
      console.error('Error al eliminar versión:', error);
      throw error;
    }
  }

  /**
   * Actualiza una sección específica del CV
   */
  static async updateSection<K extends keyof CVData>(
    section: K, 
    data: CVData[K]
  ): Promise<void> {
    try {
      const currentDocRef = doc(db, CV_COLLECTION, 'current');
      await updateDoc(currentDocRef, {
        [section]: data,
        lastUpdated: serverTimestamp()
      });
      
      console.log(`Sección ${section} actualizada exitosamente`);
    } catch (error) {
      console.error(`Error al actualizar sección ${section}:`, error);
      throw error;
    }
  }

  /**
   * Inicializa los datos del CV con datos por defecto
   */
  static async initializeCV(defaultData: CVData): Promise<void> {
    try {
      const currentCV = await this.getCurrentCV();
      if (!currentCV) {
        await this.saveCV(defaultData, 'Inicialización del CV');
        console.log('CV inicializado con datos por defecto');
      }
    } catch (error) {
      console.error('Error al inicializar CV:', error);
      throw error;
    }
  }
}
