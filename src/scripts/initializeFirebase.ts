/**
 * Script para inicializar Firebase con los datos por defecto del CV
 * Ejecutar este script la primera vez para subir los datos a Firebase
 */

import { CVService } from '../services/cvService';
import { cvData } from '../data/cvData';

export const initializeFirebaseData = async (): Promise<void> => {
  try {
    console.log('🚀 Iniciando la inicialización de Firebase...');
    
    // Verificar si ya existen datos
    const existingData = await CVService.getCurrentCV();
    
    if (existingData) {
      console.log('✅ Ya existen datos en Firebase. No es necesario inicializar.');
      return;
    }
    
    console.log('📤 Subiendo datos por defecto a Firebase...');
    
    // Subir datos por defecto
    await CVService.saveCV(cvData, 'Inicialización automática del CV con datos por defecto');
    
    console.log('✅ Datos inicializados correctamente en Firebase');
    console.log('📊 Datos subidos:');
    console.log(`   - Información personal: ${cvData.personalInfo.name}`);
    console.log(`   - Habilidades: ${cvData.skills.length} elementos`);
    console.log(`   - Educación: ${cvData.education.length} elementos`);
    console.log(`   - Educación complementaria: ${cvData.complementaryEducation.length} elementos`);
    console.log(`   - Experiencia: ${cvData.experience.length} elementos`);
    console.log(`   - Portfolio: ${cvData.portfolio.length} elementos`);
    console.log(`   - Servicios: ${cvData.services.length} elementos`);
    
  } catch (error) {
    console.error('❌ Error al inicializar Firebase:', error);
    throw error;
  }
};

// Función para ejecutar desde la consola del navegador
(window as any).initializeFirebaseData = initializeFirebaseData;
