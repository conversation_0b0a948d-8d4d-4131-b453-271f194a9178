.cv-admin-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.cv-admin-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.cv-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.cv-admin-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #e0e0e0;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px 20px;
  margin: 0;
  border-bottom: 1px solid #fcc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: none;
  border: none;
  color: #c33;
  cursor: pointer;
  font-size: 18px;
  padding: 0 5px;
}

.cv-admin-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.cv-admin-tabs button {
  background: none;
  border: none;
  padding: 15px 25px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
}

.cv-admin-tabs button:hover {
  background: #e9ecef;
  color: #333;
}

.cv-admin-tabs button.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: white;
}

.cv-admin-content {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
}

.loading {
  text-align: center;
  padding: 50px;
  font-size: 18px;
  color: #666;
}

/* Edit Section */
.save-section {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  align-items: center;
}

.save-description {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.save-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
}

.save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.form-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Skills Section */
.skills-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.skill-item {
  display: grid;
  grid-template-columns: 2fr 100px 150px auto;
  gap: 10px;
  align-items: center;
  padding: 15px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.skill-item input,
.skill-item select {
  margin: 0;
}

.add-btn,
.delete-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.add-btn {
  background: #007bff;
  color: white;
  margin-top: 15px;
  align-self: flex-start;
}

.add-btn:hover {
  background: #0056b3;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover:not(:disabled) {
  background: #c82333;
}

.delete-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Versions Section */
.versions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.versions-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.refresh-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refresh-btn:hover {
  background: #5a6268;
}

.versions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s;
}

.version-item.active {
  background: #e7f3ff;
  border-color: #007bff;
}

.version-info h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
}

.version-description {
  margin: 0 0 5px 0;
  color: #666;
  font-size: 14px;
}

.version-date {
  margin: 0;
  color: #999;
  font-size: 12px;
}

.active-badge {
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  margin-left: 10px;
}

.version-actions {
  display: flex;
  gap: 10px;
}

.restore-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.restore-btn:hover {
  background: #138496;
}

.no-versions {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
  .cv-admin-overlay {
    padding: 10px;
  }
  
  .cv-admin-modal {
    max-height: 95vh;
  }
  
  .cv-admin-content {
    padding: 20px;
  }
  
  .save-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .skill-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .version-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .version-actions {
    justify-content: flex-end;
  }
}
