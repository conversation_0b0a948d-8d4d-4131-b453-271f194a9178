import React, { useState } from 'react';
import { useCVData } from '../../hooks/useCVData';
import './CVAdmin.css';

interface CVAdminProps {
  onClose?: () => void;
}

export const CVAdmin: React.FC<CVAdminProps> = ({ onClose }) => {
  const {
    cvData,
    versions,
    loading,
    saving,
    error,
    saveCV,
    restoreVersion,
    deleteVersion,
    refreshData,
    clearError
  } = useCVData();

  const [activeTab, setActiveTab] = useState<'edit' | 'versions'>('edit');
  const [editData, setEditData] = useState(cvData);
  const [saveDescription, setSaveDescription] = useState('');

  // Actualizar editData cuando cvData cambie
  React.useEffect(() => {
    if (cvData) {
      setEditData(cvData);
    }
  }, [cvData]);

  const handleSave = async () => {
    if (!editData) return;

    try {
      await saveCV(editData, saveDescription || undefined);
      setSaveDescription('');
      alert('CV guardado exitosamente');
    } catch (err) {
      alert('Error al guardar el CV');
    }
  };

  const handleRestore = async (versionId: string) => {
    if (confirm('¿Estás seguro de que quieres restaurar esta versión?')) {
      try {
        await restoreVersion(versionId);
        alert('Versión restaurada exitosamente');
      } catch (err) {
        alert('Error al restaurar la versión');
      }
    }
  };

  const handleDelete = async (versionId: string) => {
    if (confirm('¿Estás seguro de que quieres eliminar esta versión?')) {
      try {
        await deleteVersion(versionId);
        alert('Versión eliminada exitosamente');
      } catch (err) {
        alert('Error al eliminar la versión');
      }
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Fecha no disponible';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString('es-ES');
  };

  if (loading) {
    return (
      <div className="cv-admin-overlay">
        <div className="cv-admin-modal">
          <div className="loading">Cargando datos del CV...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="cv-admin-overlay">
      <div className="cv-admin-modal">
        <div className="cv-admin-header">
          <h2>Administración del CV</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        {error && (
          <div className="error-message">
            {error}
            <button onClick={clearError}>×</button>
          </div>
        )}

        <div className="cv-admin-tabs">
          <button
            className={activeTab === 'edit' ? 'active' : ''}
            onClick={() => setActiveTab('edit')}
          >
            Editar CV
          </button>
          <button
            className={activeTab === 'versions' ? 'active' : ''}
            onClick={() => setActiveTab('versions')}
          >
            Versiones ({versions.length})
          </button>
        </div>

        <div className="cv-admin-content">
          {activeTab === 'edit' && editData && (
            <div className="edit-section">
              <div className="save-section">
                <input
                  type="text"
                  placeholder="Descripción de los cambios (opcional)"
                  value={saveDescription}
                  onChange={(e) => setSaveDescription(e.target.value)}
                  className="save-description"
                />
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="save-btn"
                >
                  {saving ? 'Guardando...' : 'Guardar CV'}
                </button>
              </div>

              <div className="edit-form">
                <div className="form-section">
                  <h3>Información Personal</h3>
                  <div className="form-group">
                    <label>Nombre:</label>
                    <input
                      type="text"
                      value={editData.personalInfo.name}
                      onChange={(e) => setEditData({
                        ...editData,
                        personalInfo: { ...editData.personalInfo, name: e.target.value }
                      })}
                    />
                  </div>
                  <div className="form-group">
                    <label>Título:</label>
                    <input
                      type="text"
                      value={editData.personalInfo.title}
                      onChange={(e) => setEditData({
                        ...editData,
                        personalInfo: { ...editData.personalInfo, title: e.target.value }
                      })}
                    />
                  </div>
                  <div className="form-group">
                    <label>Teléfono:</label>
                    <input
                      type="text"
                      value={editData.personalInfo.phone}
                      onChange={(e) => setEditData({
                        ...editData,
                        personalInfo: { ...editData.personalInfo, phone: e.target.value }
                      })}
                    />
                  </div>
                  <div className="form-group">
                    <label>Ciudad:</label>
                    <input
                      type="text"
                      value={editData.personalInfo.city}
                      onChange={(e) => setEditData({
                        ...editData,
                        personalInfo: { ...editData.personalInfo, city: e.target.value }
                      })}
                    />
                  </div>
                </div>

                <div className="form-section">
                  <h3>Acerca de</h3>
                  <div className="form-group">
                    <label>Descripción:</label>
                    <textarea
                      value={editData.about}
                      onChange={(e) => setEditData({ ...editData, about: e.target.value })}
                      rows={6}
                    />
                  </div>
                </div>

                <div className="form-section">
                  <h3>Habilidades</h3>
                  <div className="skills-list">
                    {editData.skills.map((skill, index) => (
                      <div key={index} className="skill-item">
                        <input
                          type="text"
                          value={skill.name}
                          onChange={(e) => {
                            const newSkills = [...editData.skills];
                            newSkills[index] = { ...skill, name: e.target.value };
                            setEditData({ ...editData, skills: newSkills });
                          }}
                          placeholder="Nombre de la habilidad"
                        />
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={skill.percentage}
                          onChange={(e) => {
                            const newSkills = [...editData.skills];
                            newSkills[index] = { ...skill, percentage: parseInt(e.target.value) };
                            setEditData({ ...editData, skills: newSkills });
                          }}
                          placeholder="Porcentaje"
                        />
                        <select
                          value={skill.category}
                          onChange={(e) => {
                            const newSkills = [...editData.skills];
                            newSkills[index] = { ...skill, category: e.target.value as any };
                            setEditData({ ...editData, skills: newSkills });
                          }}
                        >
                          <option value="webs">Webs</option>
                          <option value="frameworks">Frameworks</option>
                          <option value="prog">Programación</option>
                          <option value="db">Base de Datos</option>
                          <option value="apis">APIs</option>
                          <option value="sistem">Sistemas</option>
                          <option value="ofimatica">Ofimática</option>
                          <option value="otro">Otro</option>
                        </select>
                        <button
                          onClick={() => {
                            const newSkills = editData.skills.filter((_, i) => i !== index);
                            setEditData({ ...editData, skills: newSkills });
                          }}
                          className="delete-btn"
                        >
                          Eliminar
                        </button>
                      </div>
                    ))}
                    <button
                      onClick={() => {
                        const newSkills = [...editData.skills, { name: '', percentage: 0, category: 'otro' as any }];
                        setEditData({ ...editData, skills: newSkills });
                      }}
                      className="add-btn"
                    >
                      Agregar Habilidad
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'versions' && (
            <div className="versions-section">
              <div className="versions-header">
                <h3>Historial de Versiones</h3>
                <button onClick={refreshData} className="refresh-btn">
                  Actualizar
                </button>
              </div>

              <div className="versions-list">
                {versions.map((version) => (
                  <div key={version.id} className={`version-item ${version.isActive ? 'active' : ''}`}>
                    <div className="version-info">
                      <h4>{version.version}</h4>
                      <p className="version-description">{version.description}</p>
                      <p className="version-date">{formatDate(version.createdAt)}</p>
                      {version.isActive && <span className="active-badge">Activa</span>}
                    </div>
                    <div className="version-actions">
                      {!version.isActive && (
                        <button
                          onClick={() => version.id && handleRestore(version.id)}
                          className="restore-btn"
                        >
                          Restaurar
                        </button>
                      )}
                      <button
                        onClick={() => version.id && handleDelete(version.id)}
                        className="delete-btn"
                        disabled={version.isActive}
                      >
                        Eliminar
                      </button>
                    </div>
                  </div>
                ))}

                {versions.length === 0 && (
                  <div className="no-versions">
                    No hay versiones guardadas
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
