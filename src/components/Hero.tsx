import React, { useEffect, useRef } from 'react';
import Typed from 'typed.js';
import type { PersonalInfo } from '../types/cv';

interface HeroProps {
  personalInfo: PersonalInfo;
}

const Hero: React.FC<HeroProps> = ({ personalInfo }) => {
  const typedRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (typedRef.current) {
      const typed = new Typed(typedRef.current, {
        strings: [
          "FullStack Architect",
          "UX/UI Craftsman",
          "Angular/React Enthusiast",
          "Drupal Master",
          "AI Agent Builder",
          "Code Artisan"
        ],
        typeSpeed: 100,
        backSpeed: 50,
        backDelay: 2000,
        loop: true
      });

      return () => {
        typed.destroy();
      };
    }
  }, []);

  return (
    <section id="hero" className="d-flex flex-column justify-content-center align-items-center">
      <div className="hero-container" data-aos="fade-in">
        <h1>{personalInfo.name}</h1>
        <p>
          Soy <span ref={typedRef} className="typed"></span>
        </p>
      </div>
    </section>
  );
};

export default Hero;
